"use client";

import React, { useState, useRef } from "react";
import { motion, PanInfo, useAnimation } from "framer-motion";
import { Plus } from "lucide-react";
import SearchPill from "./SearchPill";

interface Wall {
  id: string;
  name: string;
  icon: keyof typeof emojiIcons;
  color: string;
}

const initialWalls: Wall[] = [
  { id: "1", name: "Main Wall", icon: "Home", color: "#FF6B6B" },
  { id: "2", name: "Work Wall", icon: "Briefcase", color: "#4ECDC4" },
  { id: "3", name: "Personal Wall", icon: "Star", color: "#FFD93D" },
  { id: "4", name: "Study Wall", icon: "Book", color: "#6BCB77" },
];

const emojiIcons = {
  Home: { icon: "🏠", label: "Home" },
  Briefcase: { icon: "💼", label: "Work" },
  Star: { icon: "⭐", label: "Personal" },
  Book: { icon: "📚", label: "Study" },
  Palette: { icon: "🎨", label: "Art" },
  Music: { icon: "🎵", label: "Music" },
  Coffee: { icon: "☕", label: "Food" },
  Dumbbell: { icon: "💪", label: "Fitness" },
  Leaf: { icon: "🍃", label: "Nature" },
  Rocket: { icon: "🚀", label: "Projects" },
};

export function WallManager() {
  const [walls, setWalls] = useState<Wall[]>(initialWalls);
  const [activeWallIndex, setActiveWallIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();

  const handleDrag = async (
    event: MouseEvent | TouchEvent | PointerEvent,
    info: PanInfo
  ) => {
    const swipeThreshold = 50;

    if (info.offset.x > swipeThreshold) {
      await controls.start({ x: "100%", opacity: 0 });
      setActiveWallIndex((prev) => (prev - 1 + walls.length) % walls.length);
    } else if (info.offset.x < -swipeThreshold) {
      await controls.start({ x: "-100%", opacity: 0 });
      setActiveWallIndex((prev) => (prev + 1) % walls.length);
    }
    controls.start({ x: 0, opacity: 1 });
  };

  const handleIconClick = async (clickedIndex: number) => {
    if (clickedIndex === activeWallIndex) return;
    setActiveWallIndex(clickedIndex);
  };

  const handleAddWall = () => {
    const newWall: Wall = {
      id: `${walls.length + 1}`,
      name: `New Wall ${walls.length + 1}`,
      icon: "Star",
      color: "#A0AEC0",
    };
    setWalls([...walls, newWall]);
    setActiveWallIndex(walls.length);
  };

  const handleSearch = (query: string) => {
    console.log("Search query:", query);
    // TODO: Implement search functionality
  };

  const renderIcon = (index: number) => {
    const isActive = index === activeWallIndex;
    const offset = index - activeWallIndex;
    const isAddButton = index === walls.length;

    return (
      <motion.div
        key={isAddButton ? "add-button" : walls[index].id}
        className="absolute cursor-pointer"
        style={{
          left: "50%",
          x: `-50%`,
          zIndex: isActive ? 10 : 1,
          pointerEvents: "auto",
        }}
        initial={false}
        animate={{
          x: `calc(${offset * 50}px - 50%)`,
          opacity: isActive ? 1 : 0.5,
          scale: isActive ? 1.2 : 0.7,
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30,
        }}
        whileHover={{ scale: isActive ? 1.3 : 0.8, opacity: 1 }}
        whileTap={{ scale: isActive ? 1.1 : 0.65 }}
        onClick={() => (isAddButton ? handleAddWall() : handleIconClick(index))}
      >
        <div
          className="flex items-center justify-center cursor-pointer select-none"
          style={{
            fontSize: isActive ? "40px" : "28px",
            transition: "all 0.3s ease",
            filter: isActive ? "drop-shadow(0 0 3px rgba(0,0,0,0.2))" : "none",
          }}
          aria-label={
            isAddButton
              ? "Add new wall"
              : `${walls[index].name} - ${emojiIcons[walls[index].icon].label}`
          }
          role={isAddButton ? "button" : "img"}
        >
          {isAddButton ? (
            <Plus size={28} />
          ) : (
            emojiIcons[walls[index].icon].icon
          )}
        </div>
      </motion.div>
    );
  };

  return (
    <motion.div
      ref={containerRef}
      className="fixed bottom-6 left-0 right-0 flex items-center justify-center h-20 overflow-visible z-50"
      drag="x"
      dragConstraints={{ left: 0, right: 0 }}
      dragElastic={0.2}
      onDragEnd={handleDrag}
      animate={controls}
      style={{ pointerEvents: "none" }}
    >
      <motion.div
        className="relative w-80 h-full"
        initial={false}
        animate={{ x: 0 }}
      >
        {[...Array(walls.length + 1)].map((_, index) => renderIcon(index))}
      </motion.div>
      {/*<SearchPill onSearch={handleSearch} />*/}
    </motion.div>
  );
}
