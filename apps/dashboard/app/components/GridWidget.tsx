"use client";

import React from "react";
import { motion } from "framer-motion";
import { GripHorizontal, Settings, X, Maximize2 } from "lucide-react";
import { Resizable } from "re-resizable";
import { Widget } from "../types/grid";
import { <PERSON><PERSON> } from "./ui/button";
import { cn } from "@/lib/utils";

// Import widget components
import CollaborativeEditorWidget from "../widgets/CollaborativeEditorWidget";
import TaskListWidget from "../widgets/TaskListWidget";
import NotesWidget from "../widgets/NotesWidget";
import ClockWidget from "../widgets/ClockWidget";

interface GridWidgetProps {
	widget: Widget;
	isSelected?: boolean;
	onClick?: () => void;
	onUpdate?: (widget: Widget) => void;
	onRemove?: (widgetId: string) => void;
	onDrag?: (widgetId: string, info: any) => void;
	onResize?: (
		widgetId: string,
		size: { width: number; height: number }
	) => void;
	gridConfig?: { cellSize: number; gap: number; containerPadding: number };
}

// Widget component registry
const WidgetComponents = {
	"collaborative-editor": CollaborativeEditorWidget,
	"task-list": TaskListWidget,
	notes: NotesWidget,
	clock: ClockWidget,
} as const;

export default function GridWidget({
	widget,
	isSelected = false,
	onClick,
	onUpdate,
	onRemove,
	onDrag,
	onResize,
	gridConfig = { cellSize: 140, gap: 12, containerPadding: 20 },
}: GridWidgetProps) {
	const WidgetComponent =
		WidgetComponents[widget.type as keyof typeof WidgetComponents];

	if (!WidgetComponent) {
		return (
			<div className="w-full h-full bg-destructive/10 border border-destructive rounded-lg flex items-center justify-center">
				<p className="text-destructive text-sm">
					Unknown widget type: {widget.type}
				</p>
			</div>
		);
	}

	// Calculate grid-snapped size
	const snapSizeToGrid = (width: number, height: number) => {
		const cols = Math.max(
			1,
			Math.round(width / (gridConfig.cellSize + gridConfig.gap))
		);
		const rows = Math.max(
			1,
			Math.round(height / (gridConfig.cellSize + gridConfig.gap))
		);
		return { cols, rows };
	};

	return (
		<motion.div
			layout
			initial={{ opacity: 0, scale: 0.8 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.8 }}
			transition={{ type: "spring", stiffness: 300, damping: 30 }}
			className="relative w-full h-full"
		>
			<Resizable
				size={{
					width: "100%",
					height: "100%",
				}}
				onResizeStop={(e, direction, ref, delta) => {
					const newSize = snapSizeToGrid(ref.offsetWidth, ref.offsetHeight);
					onResize?.(widget.id, {
						width: newSize.cols,
						height: newSize.rows,
					});
				}}
				minWidth={gridConfig.cellSize}
				minHeight={gridConfig.cellSize}
				grid={[
					gridConfig.cellSize + gridConfig.gap,
					gridConfig.cellSize + gridConfig.gap,
				]}
				enable={{
					top: false,
					right: true,
					bottom: true,
					left: false,
					topRight: false,
					bottomRight: true,
					bottomLeft: false,
					topLeft: false,
				}}
				handleStyles={{
					bottomRight: {
						background: "hsl(var(--primary))",
						width: "12px",
						height: "12px",
						borderRadius: "2px 0 6px 0",
						opacity: 0.7,
					},
					right: {
						background: "hsl(var(--primary))",
						width: "4px",
						opacity: 0.5,
					},
					bottom: {
						background: "hsl(var(--primary))",
						height: "4px",
						opacity: 0.5,
					},
				}}
				className={cn(
					"bg-card border-2 rounded-lg shadow-sm overflow-hidden group",
					"hover:shadow-md transition-shadow duration-200",
					isSelected &&
						"ring-2 ring-primary ring-offset-2 ring-offset-background border-primary"
				)}
				onClick={onClick}
			>
				{/* Widget Header */}
				<div className="absolute top-0 left-0 right-0 z-10 bg-card/95 backdrop-blur-sm border-b border-border px-3 py-2 flex items-center justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-200">
					<div className="flex items-center gap-2">
						<GripHorizontal className="h-4 w-4 text-muted-foreground cursor-move" />
						<span className="text-sm font-medium text-foreground truncate">
							{widget.title}
						</span>
					</div>

					<div className="flex items-center gap-1">
						<Button
							size="sm"
							variant="ghost"
							className="h-6 w-6 p-0"
							onClick={(e) => {
								e.stopPropagation();
								// TODO: Open widget settings
							}}
						>
							<Settings className="h-3 w-3" />
						</Button>
						<Button
							size="sm"
							variant="ghost"
							className="h-6 w-6 p-0 text-destructive hover:text-destructive"
							onClick={(e) => {
								e.stopPropagation();
								onRemove?.(widget.id);
							}}
						>
							<X className="h-3 w-3" />
						</Button>
					</div>
				</div>

				{/* Widget Content */}
				<div className="w-full h-full p-4 pt-6">
					<WidgetComponent
						widget={widget}
						onUpdate={onUpdate}
						onRemove={onRemove}
					/>
				</div>

				{/* Resize Indicator */}
				<div className="absolute bottom-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
					<Maximize2 className="h-3 w-3 text-muted-foreground" />
				</div>
			</Resizable>
		</motion.div>
	);
}
