"use client";

import { useEffect, useRef, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Collaboration from "@tiptap/extension-collaboration";
import { HocuspocusProvider } from "@hocuspocus/provider";
import * as Y from "yjs";

export default function CollabEditorPage() {
	const ydocRef = useRef(new Y.Doc());
	const [providerReady, setProviderReady] = useState(false);
	const [tasks, setTasks] = useState<string[]>([]);
	const [lastTaskAdded, setLastTaskAdded] = useState("");
	const [taskInput, setTaskInput] = useState("");

	useEffect(() => {
		const provider = new HocuspocusProvider({
			url: "ws://*************:1234",
			name: "CollabDocument",
			document: ydocRef.current,
			token: "valid-token",
			onConnect() {
				console.log("✅ Connected");
				setProviderReady(true);
				const yTasks = ydocRef.current.getArray("tasks");
				yTasks.observe(() => {
					setTasks(
						yTasks.toArray().filter((t): t is string => typeof t === "string")
					);
				});
			},
		});

		return () => {
			provider.destroy();
			console.log("🧹 Provider destroyed");
		};
	}, []);

	const editor = useEditor(
		{
			extensions: [
				StarterKit.configure({ history: false }),
				Collaboration.configure({ document: ydocRef.current }),
			] as any,
			content: "",
			immediatelyRender: false,
		},
		[providerReady]
	);

	const handleAddTask = () => {
		const task = taskInput.trim();
		if (!task) return;
		const yTasks = ydocRef.current.getArray("tasks");
		yTasks.push([task]);
		setLastTaskAdded(task);
		setTaskInput("");
	};

	const handleClearTasks = () => {
		const yTasks = ydocRef.current.getArray("tasks");
		yTasks.delete(0, yTasks.length);
		setTasks([]);
		setLastTaskAdded("");
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-white px-6">
			<div
				className="p-6 bg-white rounded shadow mt-[-180px]
	sm:ml-0 sm:max-w-2xl sm:w-full
	ml-[100px] max-w-[85vw] w-[calc(100vw-100px)]
	"
			>
				<div className="text-center">
					<h1 className="text-3xl font-bold mb-6">Collaborative Editor</h1>
				</div>

				<div className="mb-4">
					<h2 className="text-xl font-semibold mb-2 text-black">Tasks</h2>
					<ul className="list-disc list-inside text-black">
						{tasks.map((task, index) => (
							<li key={index}>{task}</li>
						))}
					</ul>
				</div>

				<div className="flex flex-col sm:flex-row sm:items-center sm:gap-2 gap-2 mb-4">
					<input
						type="text"
						value={taskInput}
						onChange={(e) => setTaskInput(e.target.value)}
						placeholder="Enter task"
						className="px-3 py-2 rounded border border-gray-300 w-full sm:w-[300px] text-black"
					/>
					<button
						onClick={handleAddTask}
						className="bg-blue-600 text-white px-4 py-2 rounded w-full sm:w-auto"
					>
						➕ Add Task
					</button>
					<button
						onClick={handleClearTasks}
						className="bg-red-600 text-white px-4 py-2 rounded w-full sm:w-auto"
					>
						🧹 Clear Tasks
					</button>
				</div>

				{lastTaskAdded && (
					<p className="text-green-600 text-center mb-4">
						✅ Added: {lastTaskAdded}
					</p>
				)}

				{editor ? (
					<EditorContent
						editor={editor}
						className="prose prose-lg max-w-none outline outline-1 outline-gray-300 p-4"
					/>
				) : (
					<p className="text-center text-lg">Loading editor...</p>
				)}
			</div>
		</div>
	);
}
