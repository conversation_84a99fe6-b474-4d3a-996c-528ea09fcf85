"use client";

import "./globals.css";
import { useState } from "react";
import DashboardNav from "@/app/components/DashboardNav";

import { WallManager } from "@/app/components/WallManager";
import { But<PERSON> } from "@/app/components/ui/button";
import { Paintbrush, Sun, Moon, CircleDot } from "lucide-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
	DropdownMenuSeparator,
} from "@/app/components/ui/dropdown-menu";

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const [theme, setTheme] = useState<
		"light" | "dark" | "blue" | "green" | "polka"
	>("light");

	return (
		<html lang="en" className={theme}>
			<body className="bg-background text-foreground">
				<div className="min-h-screen">
					<DashboardNav />
					<div className="fixed top-4 right-4 z-50">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="outline"
									size="icon"
									className="bg-background dark:bg-gray-800 border dark:border-gray-700 shadow-md hover:bg-accent dark:hover:bg-gray-700"
								>
									<Paintbrush className="h-[1.2rem] w-[1.2rem]" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent
								align="end"
								className="bg-background dark:bg-gray-800 border dark:border-gray-700 shadow-md"
							>
								<DropdownMenuItem
									onClick={() => setTheme("light")}
									className="flex items-center gap-2 cursor-pointer hover:bg-accent dark:hover:bg-gray-700"
								>
									<Sun className="h-4 w-4" />
									Light
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() => setTheme("dark")}
									className="flex items-center gap-2 cursor-pointer hover:bg-accent dark:hover:bg-gray-700"
								>
									<Moon className="h-4 w-4" />
									Dark
								</DropdownMenuItem>
								<DropdownMenuSeparator className="dark:border-gray-700" />
								<DropdownMenuItem
									onClick={() => setTheme("blue")}
									className="flex items-center gap-2 cursor-pointer hover:bg-accent dark:hover:bg-gray-700"
								>
									<div className="h-4 w-4 rounded-full bg-blue-500" />
									Blue
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() => setTheme("green")}
									className="flex items-center gap-2 cursor-pointer hover:bg-accent dark:hover:bg-gray-700"
								>
									<div className="h-4 w-4 rounded-full bg-green-500" />
									Green
								</DropdownMenuItem>
								<DropdownMenuSeparator className="dark:border-gray-700" />
								<DropdownMenuItem
									onClick={() => setTheme("polka")}
									className="flex items-center gap-2 cursor-pointer hover:bg-accent dark:hover:bg-gray-700"
								>
									<CircleDot className="h-4 w-4" />
									Polka Dot
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
					<main className="relative z-10 min-h-screen p-4">{children}</main>
					<WallManager />
				</div>
			</body>
		</html>
	);
}
