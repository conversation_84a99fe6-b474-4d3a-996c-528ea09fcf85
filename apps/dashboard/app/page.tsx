"use client";

import { useEffect, useRef, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Collaboration from "@tiptap/extension-collaboration";
import { HocuspocusProvider } from "@hocuspocus/provider";
import * as Y from "yjs";

export default function CollabEditorPage() {
	const ydocRef = useRef(new Y.Doc());
	const [providerReady, setProviderReady] = useState(false);
	const [tasks, setTasks] = useState<string[]>([]);
	const [lastTaskAdded, setLastTaskAdded] = useState("");
	const [taskInput, setTaskInput] = useState("");

	useEffect(() => {
		const provider = new HocuspocusProvider({
			url: "ws://*************:1234",
			name: "CollabDocument",
			document: ydocRef.current,
			token: "valid-token",
			onConnect() {
				console.log("✅ Connected");
				setProviderReady(true);
				const yTasks = ydocRef.current.getArray("tasks");
				yTasks.observe(() => {
					setTasks(
						yTasks.toArray().filter((t): t is string => typeof t === "string")
					);
				});
			},
		});

		return () => {
			provider.destroy();
			console.log("🧹 Provider destroyed");
		};
	}, []);

	const editor = useEditor(
		{
			extensions: [
				StarterKit.configure({ history: false }),
				Collaboration.configure({ document: ydocRef.current }),
			] as any,
			content: "",
			immediatelyRender: false,
		},
		[providerReady]
	);

	const handleAddTask = () => {
		const task = taskInput.trim();
		if (!task) return;
		const yTasks = ydocRef.current.getArray("tasks");
		yTasks.push([task]);
		setLastTaskAdded(task);
		setTaskInput("");
	};

	const handleClearTasks = () => {
		const yTasks = ydocRef.current.getArray("tasks");
		yTasks.delete(0, yTasks.length);
		setTasks([]);
		setLastTaskAdded("");
	};

	return (
		<div className="min-h-screen flex items-center justify-center px-4 py-8">
			<div className="w-full max-w-4xl mx-auto">
				<div className="bg-card border border-border rounded-lg shadow-sm p-6 md:p-8">
					<div className="text-center mb-8">
						<h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
							Collaborative Editor
						</h1>
						<p className="text-muted-foreground">
							Real-time collaborative editing with shared tasks
						</p>
					</div>

					<div className="mb-6">
						<h2 className="text-xl font-semibold mb-3 text-foreground">
							Tasks
						</h2>
						{tasks.length > 0 ? (
							<ul className="space-y-2">
								{tasks.map((task, index) => (
									<li
										key={index}
										className="flex items-center gap-2 p-2 bg-muted rounded-md"
									>
										<span className="text-primary">•</span>
										<span className="text-foreground">{task}</span>
									</li>
								))}
							</ul>
						) : (
							<p className="text-muted-foreground italic">
								No tasks yet. Add one below!
							</p>
						)}
					</div>

					<div className="space-y-3 mb-6">
						<div className="flex flex-col sm:flex-row gap-3">
							<input
								type="text"
								value={taskInput}
								onChange={(e) => setTaskInput(e.target.value)}
								onKeyDown={(e) => e.key === "Enter" && handleAddTask()}
								placeholder="Enter a new task..."
								className="flex-1 px-4 py-2 bg-background border border-input rounded-md text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
							/>
							<div className="flex gap-2">
								<button
									onClick={handleAddTask}
									disabled={!taskInput.trim()}
									className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
								>
									<span>➕</span>
									<span className="hidden sm:inline">Add Task</span>
								</button>
								<button
									onClick={handleClearTasks}
									disabled={tasks.length === 0}
									className="px-4 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
								>
									<span>🧹</span>
									<span className="hidden sm:inline">Clear</span>
								</button>
							</div>
						</div>
					</div>

					{lastTaskAdded && (
						<div className="mb-6 p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-md">
							<p className="text-green-700 dark:text-green-300 text-center">
								✅ Added: <strong>{lastTaskAdded}</strong>
							</p>
						</div>
					)}

					<div className="border border-border rounded-md">
						<div className="bg-muted px-4 py-2 border-b border-border">
							<h3 className="text-sm font-medium text-foreground">
								Collaborative Editor
							</h3>
						</div>
						<div className="p-4">
							{editor ? (
								<EditorContent
									editor={editor}
									className="prose prose-sm max-w-none focus:outline-none min-h-[200px] text-foreground"
								/>
							) : (
								<div className="flex items-center justify-center min-h-[200px]">
									<p className="text-muted-foreground">Loading editor...</p>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
